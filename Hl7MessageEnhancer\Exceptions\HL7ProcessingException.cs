namespace Hl7MessageEnhancer.Exceptions;

/// <summary>
/// Custom exception for HL7 processing errors
/// </summary>
public class HL7ProcessingException : Exception
{
    /// <summary>
    /// Error code for categorizing the type of error
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// File path where the error occurred (if applicable)
    /// </summary>
    public string? FilePath { get; }

    /// <summary>
    /// Initializes a new instance of the HL7ProcessingException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errorCode">The error code</param>
    /// <param name="filePath">The file path where the error occurred</param>
    public HL7ProcessingException(string message, string errorCode, string? filePath = null)
        : base(message)
    {
        ErrorCode = errorCode;
        FilePath = filePath;
    }

    /// <summary>
    /// Initializes a new instance of the HL7ProcessingException class with an inner exception
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errorCode">The error code</param>
    /// <param name="innerException">The inner exception</param>
    /// <param name="filePath">The file path where the error occurred</param>
    public HL7ProcessingException(string message, string errorCode, Exception innerException, string? filePath = null)
        : base(message, innerException)
    {
        ErrorCode = errorCode;
        FilePath = filePath;
    }
}
