﻿using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;
using Hl7MessageEnhancer.Models;
using Hl7MessageEnhancer.Exceptions;
using Newtonsoft.Json;
using NHapi.Base.Parser;

namespace TestMessageEnhancer;

public class TestHl7MessageEnhancer : IDisposable
{
    private readonly ILogger<HL7Processor> _logger;
    private readonly string _testDataDirectory;

    public TestHl7MessageEnhancer()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = loggerFactory.CreateLogger<HL7Processor>();
        _testDataDirectory = Path.Combine(Directory.GetCurrentDirectory(), "TestData");
        Directory.CreateDirectory(_testDataDirectory);
    }

    [Fact]
    public void HL7Processor_Constructor_ShouldInitializeCorrectly()
    {
        // Arrange
        var sourceDir = Path.Combine(_testDataDirectory, "source");
        var outputDir = Path.Combine(_testDataDirectory, "output");
        Directory.CreateDirectory(sourceDir);

        // Act
        var processor = new HL7Processor(_logger, sourceDir, outputDir, false);

        // Assert
        processor.Should().NotBeNull();
        processor.Statistics.Should().NotBeNull();
        processor.Statistics.FilesProcessed.Should().Be(0);
        processor.Statistics.FilesEnhanced.Should().Be(0);
        processor.Statistics.ErrorsEncountered.Should().Be(0);
        processor.Statistics.FilesQuarantined.Should().Be(0);
    }

    [Fact]
    public void HL7Processor_Constructor_WithInvalidSourceDirectory_ShouldThrowException()
    {
        // Arrange
        var invalidSourceDir = Path.Combine(_testDataDirectory, "nonexistent");
        var outputDir = Path.Combine(_testDataDirectory, "output");

        // Act & Assert
        var action = () => new HL7Processor(_logger, invalidSourceDir, outputDir, false);
        action.Should().NotThrow(); // Constructor doesn't validate source directory existence
    }

    [Fact]
    public void ProcessingStatistics_Reset_ShouldResetAllCounters()
    {
        // Arrange
        var stats = new ProcessingStatistics
        {
            FilesProcessed = 5,
            FilesEnhanced = 3,
            ErrorsEncountered = 1,
            FilesQuarantined = 1
        };

        // Act
        stats.Reset();

        // Assert
        stats.FilesProcessed.Should().Be(0);
        stats.FilesEnhanced.Should().Be(0);
        stats.ErrorsEncountered.Should().Be(0);
        stats.FilesQuarantined.Should().Be(0);
    }

    [Fact]
    public void MappingRule_Deserialization_ShouldWorkCorrectly()
    {
        // Arrange
        var json = @"[
            {
                ""obxField"": ""OBX-3.1"",
                ""obxValue"": ""TEST_VALUE"",
                ""targetSegment"": ""PID"",
                ""targetField"": ""30"",
                ""removeOriginal"": true,
                ""description"": ""Test mapping rule"",
                ""valueMapping"": {
                    ""*"": ""Y""
                }
            }
        ]";

        // Act
        var rules = JsonConvert.DeserializeObject<List<MappingRule>>(json);

        // Assert
        rules.Should().NotBeNull();
        rules.Should().HaveCount(1);
        var rule = rules![0];
        rule.ObxField.Should().Be("OBX-3.1");
        rule.ObxValue.Should().Be("TEST_VALUE");
        rule.TargetSegment.Should().Be("PID");
        rule.TargetField.Should().Be("30");
        rule.RemoveOriginal.Should().BeTrue();
        rule.Description.Should().Be("Test mapping rule");
        rule.ValueMapping.Should().NotBeNull();
        rule.ValueMapping!["*"].Should().Be("Y");
    }

    [Fact]
    public void HL7ProcessingException_Constructor_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var message = "Test error message";
        var errorCode = "TEST_ERROR";
        var filePath = "test.hl7";

        // Act
        var exception = new HL7ProcessingException(message, errorCode, filePath);

        // Assert
        exception.Message.Should().Be(message);
        exception.ErrorCode.Should().Be(errorCode);
        exception.FilePath.Should().Be(filePath);
    }

    [Fact]
    public void HL7ProcessingException_ConstructorWithInnerException_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var message = "Test error message";
        var errorCode = "TEST_ERROR";
        var innerException = new InvalidOperationException("Inner exception");
        var filePath = "test.hl7";

        // Act
        var exception = new HL7ProcessingException(message, errorCode, innerException, filePath);

        // Assert
        exception.Message.Should().Be(message);
        exception.ErrorCode.Should().Be(errorCode);
        exception.FilePath.Should().Be(filePath);
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void HL7Parser_ParseValidMessage_ShouldSucceed()
    {
        // Arrange
        var parser = new PipeParser();
        var hl7Message = "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.3||||||8859/1\r" +
                        "EVN|A04|20231019232741|||56269^Bhuvaneswari^Gopika^Raj^^^^^External Id^Personnel^^^External Identifier^\\R\\56269\r" +
                        "PID|1||HC02287391^^^MRN^MR^RHAPSODY_CON_SYS||ABED^AHMED^^^^^official||19930804000000|male||Non National|||||||||||||||Yemeni||No";

        // Act
        var action = () => parser.Parse(hl7Message);

        // Assert
        action.Should().NotThrow();
        var message = parser.Parse(hl7Message);
        message.Should().NotBeNull();
        message.GetStructure("MSH").Should().NotBeNull();
        message.GetStructure("EVN").Should().NotBeNull();
        message.GetStructure("PID").Should().NotBeNull();
    }

    [Fact]
    public void HL7Parser_ParseInvalidMessage_ShouldThrow()
    {
        // Arrange
        var parser = new PipeParser();
        var invalidHl7Message = "INVALID|MESSAGE|FORMAT";

        // Act & Assert
        var action = () => parser.Parse(invalidHl7Message);
        action.Should().Throw<Exception>();
    }

    private void CleanupTestDirectory()
    {
        if (Directory.Exists(_testDataDirectory))
        {
            Directory.Delete(_testDataDirectory, true);
        }
    }

    public void Dispose()
    {
        CleanupTestDirectory();
        GC.SuppressFinalize(this);
    }
}