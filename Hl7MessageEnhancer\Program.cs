﻿using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;
using Hl7MessageEnhancer.Exceptions;

namespace Hl7MessageEnhancer;

/// <summary>
/// Main program entry point for HL7 Message Enhancement Engine
/// </summary>
public class Program
{
    /// <summary>
    /// Main entry point
    /// </summary>
    /// <param name="args">Command line arguments</param>
    /// <returns>Exit code</returns>
    public static int Main(string[] args)
    {
        try
        {
            var options = ParseArguments(args);

            // Validate source directory
            if (!Directory.Exists(options.SourceDirectory))
            {
                Console.WriteLine($"ERROR: Source directory not found: {options.SourceDirectory}");
                return 1;
            }

            // Setup logging
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(options.Verbose ? LogLevel.Debug : LogLevel.Information);
            });

            var logger = loggerFactory.CreateLogger<HL7Processor>();

            // Create and run processor
            var processor = new HL7Processor(logger, options.SourceDirectory, options.OutputDirectory, options.Verbose);
            processor.Run();

            return 0;
        }
        catch (HL7ProcessingException ex)
        {
            Console.WriteLine($"ERROR: {ex.Message}");
            return 1;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ERROR: Failed to initialize processor: {ex.Message}");
            return 1;
        }
    }

    /// <summary>
    /// Parse command line arguments
    /// </summary>
    /// <param name="args">Command line arguments</param>
    /// <returns>Parsed options</returns>
    private static CommandLineOptions ParseArguments(string[] args)
    {
        var options = new CommandLineOptions();

        for (var i = 0; i < args.Length; i++)
        {
            switch (args[i])
            {
                case "--source":
                    if (i + 1 < args.Length)
                    {
                        options.SourceDirectory = args[++i];
                    }
                    break;
                case "--output":
                    if (i + 1 < args.Length)
                    {
                        options.OutputDirectory = args[++i];
                    }
                    break;
                case "--verbose":
                case "-v":
                    options.Verbose = true;
                    break;
                case "--help":
                case "-h":
                    ShowHelp();
                    Environment.Exit(0);
                    break;
            }
        }

        return options;
    }

    /// <summary>
    /// Show help information
    /// </summary>
    private static void ShowHelp()
    {
        Console.WriteLine("HL7 Message Enhancement Engine");
        Console.WriteLine();
        Console.WriteLine("Usage: Hl7MessageEnhancer [options]");
        Console.WriteLine();
        Console.WriteLine("Options:");
        Console.WriteLine("  --source <directory>    Source directory for raw HL7 files (default: rawhl7messages)");
        Console.WriteLine("  --output <directory>    Output directory for enhanced files (default: enhancedHl7)");
        Console.WriteLine("  --verbose, -v           Enable verbose logging");
        Console.WriteLine("  --help, -h              Show this help message");
    }
}

/// <summary>
/// Command line options
/// </summary>
public class CommandLineOptions
{
    /// <summary>
    /// Source directory for raw HL7 files
    /// </summary>
    public string SourceDirectory { get; set; } = "rawhl7messages";

    /// <summary>
    /// Output directory for enhanced files
    /// </summary>
    public string OutputDirectory { get; set; } = "enhancedHl7";

    /// <summary>
    /// Enable verbose logging
    /// </summary>
    public bool Verbose { get; set; }
}